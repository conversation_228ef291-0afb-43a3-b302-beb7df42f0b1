.help-title, .help-group {
  background-image: -webkit-gradient(linear, left top, left bottom, from(#7a7a9f), to(#403544));
  background-image: linear-gradient(180deg, #7a7a9f, #403544);
  -webkit-background-clip: text;
          background-clip: text;
  color: transparent;
}

:root {
  --table-color: #865E58;
}

body {
  width: 1100px;
}

.container {
  background-image: url(./main.png);
  background-position: right center;
  background-repeat: no-repeat;
  background-size: cover;
  padding: 20px 25px 10px 25px;
  width: 1130px;
}

.head-box {
  margin: 60px 0 0 0;
  padding-bottom: 0;
}
.head-box .title {
  font-size: 50px;
  color: #FFFFFF;
}
.head-box .label {
  color: #FFFFFF;
}

.cont-box {
  margin-top: 20px;
  margin-bottom: 30px;
  overflow: hidden;
  position: relative;
  font-size: 20px;
  background: rgba(255, 255, 255, 0.45);
  -webkit-box-shadow: 0 8px 32px 0 rgba(51, 51, 52, 0.5), inset 3px 3px 10px 0px rgba(255, 255, 255, 0.8);
          box-shadow: 0 8px 32px 0 rgba(51, 51, 52, 0.5), inset 3px 3px 10px 0px rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 30px;
}

.help-group {
  font-size: 25px;
  font-weight: bold;
  padding: 15px 15px 10px 20px;
  display: inline-block;
}

.help-table {
  text-align: center;
  border-collapse: collapse;
  margin: 0;
  border-radius: 0 0 10px 10px;
  display: table;
  overflow: hidden;
  width: 100%;
  color: var(--table-color);
}
.help-table .tr {
  display: table-row;
}
.help-table .tr:last-child .td {
  padding-bottom: 12px;
}
.help-table .th {
  background: rgba(34, 41, 51, 0.5);
}

.help-table .td,
.help-table .th {
  font-size: 14px;
  display: table-cell;
  -webkit-box-shadow: 0 0 1px 0 #777 inset;
          box-shadow: 0 0 1px 0 #777 inset;
  padding: 12px 0 12px 50px;
  line-height: 24px;
  position: relative;
  text-align: left;
  width: 33%;
}

.help-icon {
  width: 40px;
  height: 40px;
  display: block;
  position: absolute;
  background: url("icon.png") 0 0 no-repeat;
  background-size: 500px auto;
  border-radius: 5px;
  left: 6px;
  top: 12px;
  -webkit-transform: scale(0.85);
      -ms-transform: scale(0.85);
          transform: scale(0.85);
}

.help-title {
  display: inline-block;
  font-size: 22px;
  line-height: 24px;
}

.help-desc {
  display: block;
  font-size: 19px;
  line-height: 18px;
}