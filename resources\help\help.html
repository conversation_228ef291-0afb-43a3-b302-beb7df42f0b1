<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="content-type" content="text/html;charset=utf-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <link rel="stylesheet" type="text/css" href="{{pluResPath}}help/help.css"/>
  <link rel="shortcut icon" href="#"/>
  <style>
    .container {
      background: url("{{pluResPath}}help/imgs/default.jpg") center !important;
      background-size: cover !important;
    }
  </style>
</head>
<body>
  <div class="container" id="container">
    <div class="info-box">
      <div class="head-box">
        <div class="title">{{helpCfg.title||"使用帮助"}}</div>
        <div class="label">{{helpCfg.subTitle || "Yunzai-Bot & werewolf-plugin"}}</div>
      </div>
    </div>

    {{each helpGroup group}}
    {{set len = group?.list?.length || 0 }}
    <div class="cont-box">
      <div class="help-group">{{group.group}}</div>
      {{if len > 0}}
      <div class="help-table">
        <div class="tr">
          {{each group.list help idx}}
          <div class="td">
        <span class="help-icon" style="{{help.css}}"></span>
            <strong class="help-title">{{help.title}}</strong>
            <span class="help-desc">{{help.desc}}</span>
          </div>
      {{if idx%colCount === colCount-1 && idx>0 && idx< len-1}} </div>
            <div class="tr">
              {{/if}}
              {{/each}}
          <% for(let i=(len-1)%colCount; i< colCount-1 ; i++){ %>
                <div class="td"></div>
                <% } %>
            </div>
        </div>
        {{/if}}
      </div>
      {{/each}}
    </div>
  </body>
</html>
