@charset "UTF-8";
* {
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

body {
  background-color: #f5f5f7;
  background-image: url("imgs/bg.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
  color: #1d1d1f;
  padding: 18px;
  min-height: 100vh;
  line-height: 1.4;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.vote-container {
  max-width: 650px;
  margin: 0 auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  gap: 14.4px;
  padding-bottom: 24px;
}

.vote-row {
  position: relative;
  border-radius: 16px;
  padding: 18px 20px;
  margin-bottom: 8px;
  min-height: 76px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  overflow: visible;
}

.vote-badge {
  position: absolute;
  top: -14px;
  left: 8px;
  background-color: #ff453a;
  color: white;
  font-weight: 600;
  padding: 3px 8px;
  border-radius: 6px;
  font-size: 0.8em;
  letter-spacing: 0.3px;
  -webkit-box-shadow: 0 2px 6px rgba(255, 69, 58, 0.4);
          box-shadow: 0 2px 6px rgba(255, 69, 58, 0.4);
  border: 1.5px solid white;
  z-index: 30;
}

.vote-row-target {
  background-color: rgba(255, 255, 255, 0.9);
  margin-top: 16px;
  border-left: 4px solid #ff453a;
}

.vote-row-normal {
  background-color: rgba(255, 255, 255, 0.9);
}

.vote-row-abstain {
  background-color: rgba(255, 255, 255, 0.9);
  border-left: 4px solid #8e8e93;
}

.vote-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
  gap: 24px;
  width: 100%;
}

.target-content {
  position: relative;
  z-index: 1;
  padding-top: 5px;
}

.vote-label-container {
  position: absolute;
  z-index: 20;
}

.vote-label {
  color: white;
  font-weight: 600;
  min-width: 40px;
  text-align: center;
  font-size: 0.85em;
  padding: 4px 12px;
  border-radius: 0 0 8px 0;
  letter-spacing: 0.3px;
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 20;
}

.inline-label {
  background-color: #8e8e93;
  color: white;
  font-weight: 500;
  border-radius: 6px;
  margin-right: 5px;
  padding: 4px 10px;
  font-size: 0.9em;
  letter-spacing: 0.3px;
}

.vote-row-target .vote-label {
  background-color: #ff453a;
}

.vote-row-abstain .vote-label {
  background-color: #8e8e93;
}

.vote-arrow-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  min-width: 40px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}

.vote-arrow {
  width: 20px;
  height: 20px;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  border-right: 20px solid #06c;
  -webkit-filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
          filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
  opacity: 0.95;
}

.player-numbers {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  gap: 12px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.player-numbers.target {
  min-width: 60px;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  -webkit-box-flex: 0;
      -ms-flex: 0 0 auto;
          flex: 0 0 auto;
}

.player-numbers.voters {
  -webkit-box-flex: 0;
      -ms-flex-positive: 0;
          flex-grow: 0;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.number {
  width: 42px;
  height: 42px;
  background-color: #f5f5f7;
  color: #1d1d1f;
  border-radius: 50%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
  font-weight: 600;
  font-size: 1.05em;
  -webkit-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  position: relative;
  border: 2px solid white;
  z-index: 1;
}
.number.sheriff {
  background-color: #ffcc00;
  color: #1d1d1f;
  border: 2px solid rgba(255, 255, 255, 0.8);
}
.number.sheriff::after {
  content: "★";
  position: absolute;
  top: -10px;
  right: -6px;
  font-size: 14px;
  color: #ffcc00;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
  -webkit-filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
          filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}
.number.selected {
  background-color: #ff453a;
  color: #fff;
  -webkit-box-shadow: 0 4px 12px rgba(255, 69, 58, 0.35);
          box-shadow: 0 4px 12px rgba(255, 69, 58, 0.35);
  border: 2px solid rgba(255, 255, 255, 0.8);
  -webkit-transform: scale(1.08);
      -ms-transform: scale(1.08);
          transform: scale(1.08);
}

.player-numbers.target .number {
  width: 46px;
  height: 46px;
  font-size: 1.15em;
  position: relative;
  z-index: 2;
}

.vote-result-bar {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 14px;
  text-align: center;
  font-weight: 600;
  border-radius: 12px;
  margin-top: 14px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  letter-spacing: 0.5px;
  -webkit-box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
          box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.1);
}